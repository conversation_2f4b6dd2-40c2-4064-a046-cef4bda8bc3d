#pragma once

#include <juce_audio_processors/juce_audio_processors.h>
#include <memory>
#include <vector>
#include <string>
#include <map>
#include <mutex>

// Forward declarations for Ardour VST3 types
namespace Steinberg {
    class VST3PI;
    namespace Vst {
        class IComponent;
        class IEditController;
    }
}

using namespace juce;

/**
 * VST3 Host implementation based on Ardour's VST3 architecture
 * Manages plugin loading, parameter control, and audio processing
 */
class VST3Host
{
public:
    struct PluginInfo {
        String id;
        String name;
        String vendor;
        String category;
        String path;
        int numInputs = 0;
        int numOutputs = 0;
        bool isInstrument = false;
    };

    struct PluginInstance {
        String id;
        String pluginId;
        String chainId;
        int position = 0;
        bool bypassed = false;
        std::unique_ptr<Steinberg::VST3PI> plugin;
        std::map<int, float> parameters;
    };

    enum class ChainType {
        DeckA,
        DeckB,
        Mic,
        Master
    };

    VST3Host(bool bridgeMode = false);
    ~VST3Host();

    // Initialization
    bool initialize();
    void shutdown();

    // Plugin discovery and management
    std::vector<PluginInfo> getAvailablePlugins() const;
    bool scanPlugins(const String& directory = "");
    
    // Plugin instance management
    String loadPlugin(const String& chainId, const String& pluginId, int position = -1);
    bool removePlugin(const String& chainId, const String& instanceId);
    bool movePlugin(const String& chainId, const String& instanceId, int newPosition);

    // Parameter control
    bool setParameter(const String& instanceId, int parameterId, float value);
    float getParameter(const String& instanceId, int parameterId) const;
    std::vector<std::pair<int, String>> getParameterList(const String& instanceId) const;

    // Editor management
    bool showEditor(const String& instanceId);
    bool hideEditor(const String& instanceId);
    bool hasEditor(const String& instanceId) const;

    // Chain management
    String saveChain(const String& chainId) const;
    bool loadChain(const String& chainId, const String& jsonData);
    std::vector<String> getChainIds() const;

    // Audio processing
    void processAudio(const String& chainId, 
                     AudioBuffer<float>& buffer, 
                     int numSamples);

    // Metering and analysis
    std::map<String, float> getCurrentMeters() const;

    // Bridge mode (crash protection)
    bool isBridgeMode() const { return bridgeMode; }
    void restartCrashedPlugin(const String& instanceId);

private:
    bool bridgeMode;
    std::vector<PluginInfo> availablePlugins;
    std::map<String, std::unique_ptr<PluginInstance>> pluginInstances;
    std::map<String, std::vector<String>> chains; // chainId -> instanceIds
    
    mutable std::mutex pluginMutex;
    mutable std::mutex chainMutex;
    
    // Plugin discovery helpers
    void scanDirectory(const String& directory);
    void scanVST3Bundle(const File& bundleFile);
    PluginInfo createPluginInfo(const String& path, Steinberg::Vst::IComponent* component);
    
    // Instance management helpers
    String generateInstanceId() const;
    ChainType getChainType(const String& chainId) const;
    
    // Audio processing helpers
    void processPluginChain(const std::vector<String>& instanceIds,
                           AudioBuffer<float>& buffer,
                           int numSamples);
    
    // Bridge mode helpers
    void launchBridgedPlugin(const String& instanceId);
    bool checkPluginHealth(const String& instanceId);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(VST3Host)
};
