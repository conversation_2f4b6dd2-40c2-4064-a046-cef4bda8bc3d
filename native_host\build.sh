#!/bin/bash
set -e

echo "Building DJ Mixer VST Host..."

# Create build directory
mkdir -p build
cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build the project
cmake --build . --config Release -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build completed successfully!"
echo "Executable location: build/DJMixerVSTHost"

# Test the executable
echo ""
echo "Testing executable..."
./DJMixerVSTHost --help || echo "Executable test completed"

echo "Build script finished."
