#pragma once

#include <juce_audio_devices/juce_audio_devices.h>
#include <juce_audio_basics/juce_audio_basics.h>
#include <memory>
#include <vector>
#include <string>
#include <atomic>
#include <mutex>

using namespace juce;

/**
 * Audio Engine for DJ Mixer VST Host
 * Based on Ardour's AudioEngine architecture
 * Handles low-latency audio I/O with microphone support
 */
class AudioEngine : public AudioIODeviceCallback
{
public:
    AudioEngine();
    ~AudioEngine();

    // Initialization and shutdown
    bool initialize();
    void shutdown();

    // Device management
    StringArray getAvailableDeviceNames() const;
    bool setAudioDevice(const String& deviceName);
    bool setBufferSize(int bufferSize);
    bool setSampleRate(double sampleRate);

    // Audio callback interface
    void audioDeviceIOCallbackWithContext(const float* const* inputChannelData,
                                        int numInputChannels,
                                        float* const* outputChannelData,
                                        int numOutputChannels,
                                        int numSamples,
                                        const AudioIODeviceCallbackContext& context) override;

    void audioDeviceAboutToStart(AudioIODevice* device) override;
    void audioDeviceStopped() override;

    // Audio routing
    void connectMicInput(int inputChannel);
    void disconnectMicInput();
    bool isMicConnected() const { return micInputChannel >= 0; }

    // Metering
    float getMicLevel() const { return micLevel.load(); }
    float getMasterLevel() const { return masterLevel.load(); }
    
    // Audio processing chain access
    void setMicGain(float gain) { micGain.store(gain); }
    void setMasterGain(float gain) { masterGain.store(gain); }
    void setMicMuted(bool muted) { micMuted.store(muted); }

    // Real-time safe audio buffer access
    AudioBuffer<float>& getMicBuffer() { return micBuffer; }
    AudioBuffer<float>& getMasterBuffer() { return masterBuffer; }

    // Latency information
    int getCurrentBufferSize() const;
    double getCurrentSampleRate() const;
    double getLatencyMs() const;

private:
    std::unique_ptr<AudioDeviceManager> deviceManager;
    AudioBuffer<float> micBuffer;
    AudioBuffer<float> masterBuffer;
    
    // Mic input routing
    int micInputChannel = -1;
    std::atomic<float> micGain{1.0f};
    std::atomic<float> masterGain{1.0f};
    std::atomic<bool> micMuted{false};
    
    // Level metering (atomic for thread safety)
    std::atomic<float> micLevel{0.0f};
    std::atomic<float> masterLevel{0.0f};
    
    // Audio processing state
    std::atomic<bool> isProcessing{false};
    int currentBufferSize = 64;
    double currentSampleRate = 44100.0;
    
    // Thread safety
    mutable std::mutex deviceMutex;
    
    // Helper methods
    void updateMeters(const float* const* inputData, 
                     float* const* outputData,
                     int numInputChannels,
                     int numOutputChannels, 
                     int numSamples);
    
    void processMicInput(const float* const* inputData,
                        float* const* outputData,
                        int numInputChannels,
                        int numOutputChannels,
                        int numSamples);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioEngine)
};
