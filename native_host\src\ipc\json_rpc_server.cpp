#include "json_rpc_server.h"
#include "../audio/audio_engine.h"
#include "../vst3/vst3_host.h"
#include <iostream>
#include <sstream>

JsonRpcServer::JsonRpcServer(AudioEngine* audioEngine, VST3Host* vst3Host)
    : audioEngine(audioEngine), vst3Host(vst3Host)
{
    registerAllMethods();
}

JsonRpcServer::~JsonRpcServer()
{
    stop();
}

bool JsonRpcServer::start()
{
    if (running.load()) {
        return true;
    }
    
    running.store(true);
    
    // Start server thread for handling requests
    serverThread = std::make_unique<std::thread>(&JsonRpcServer::serverLoop, this);
    
    // Start metrics thread for periodic updates
    metricsThread = std::make_unique<std::thread>(&JsonRpcServer::metricsLoop, this);
    
    std::cout << "JSON-RPC server started" << std::endl;
    return true;
}

void JsonRpcServer::stop()
{
    if (!running.load()) {
        return;
    }
    
    running.store(false);
    
    if (serverThread && serverThread->joinable()) {
        serverThread->join();
    }
    
    if (metricsThread && metricsThread->joinable()) {
        metricsThread->join();
    }
    
    std::cout << "JSON-RPC server stopped" << std::endl;
}

void JsonRpcServer::registerMethod(const std::string& methodName, MethodHandler handler)
{
    methods[methodName] = handler;
}

void JsonRpcServer::serverLoop()
{
    std::string line;
    
    while (running.load()) {
        if (std::getline(std::cin, line)) {
            if (!line.empty()) {
                processRequest(line);
            }
        } else {
            // EOF or error
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
}

void JsonRpcServer::metricsLoop()
{
    while (running.load()) {
        // Send meter updates every 33ms (30 Hz)
        try {
            json metersUpdate;
            metersUpdate["jsonrpc"] = "2.0";
            metersUpdate["method"] = "metersUpdate";
            metersUpdate["params"] = handleGetMeters(json::object());
            
            sendResponse(metersUpdate);
        } catch (const std::exception& e) {
            std::cerr << "Error in metrics loop: " << e.what() << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(33));
    }
}

void JsonRpcServer::processRequest(const std::string& line)
{
    try {
        json request = json::parse(line);
        
        if (!request.contains("jsonrpc") || request["jsonrpc"] != "2.0") {
            sendError(-1, ERROR_INVALID_REQUEST, "Invalid JSON-RPC version");
            return;
        }
        
        if (!request.contains("method")) {
            sendError(-1, ERROR_INVALID_REQUEST, "Missing method");
            return;
        }
        
        std::string method = request["method"];
        json params = request.contains("params") ? request["params"] : json::object();
        int id = request.contains("id") ? request["id"] : -1;
        
        auto methodIt = methods.find(method);
        if (methodIt == methods.end()) {
            sendError(id, ERROR_METHOD_NOT_FOUND, "Method not found: " + method);
            return;
        }
        
        json result = methodIt->second(params);
        sendResponse(createSuccessResponse(id, result));
        
    } catch (const json::parse_error& e) {
        sendError(-1, ERROR_PARSE_ERROR, "Parse error: " + std::string(e.what()));
    } catch (const std::exception& e) {
        sendError(-1, ERROR_INTERNAL_ERROR, "Internal error: " + std::string(e.what()));
    }
}

void JsonRpcServer::sendResponse(const json& response)
{
    std::cout << response.dump() << std::endl;
    std::cout.flush();
}

void JsonRpcServer::sendError(int id, int code, const std::string& message)
{
    sendResponse(createErrorResponse(id, code, message));
}

json JsonRpcServer::handleListPlugins(const json& params)
{
    json result = json::array();
    
    auto plugins = vst3Host->getAvailablePlugins();
    for (const auto& plugin : plugins) {
        json pluginJson;
        pluginJson["id"] = plugin.id.toStdString();
        pluginJson["name"] = plugin.name.toStdString();
        pluginJson["vendor"] = plugin.vendor.toStdString();
        pluginJson["category"] = plugin.category.toStdString();
        pluginJson["numInputs"] = plugin.numInputs;
        pluginJson["numOutputs"] = plugin.numOutputs;
        pluginJson["isInstrument"] = plugin.isInstrument;
        result.push_back(pluginJson);
    }
    
    return result;
}

json JsonRpcServer::handleGetMeters(const json& params)
{
    json result;
    
    // Audio engine meters
    result["mic"] = audioEngine->getMicLevel();
    result["master"] = audioEngine->getMasterLevel();
    
    // VST3 host meters
    auto vstMeters = vst3Host->getCurrentMeters();
    for (const auto& meter : vstMeters) {
        result[meter.first.toStdString()] = meter.second;
    }
    
    return result;
}

json JsonRpcServer::handleLoadPlugin(const json& params)
{
    if (!params.contains("chainId") || !params.contains("pluginId")) {
        throw std::invalid_argument("Missing required parameters: chainId, pluginId");
    }
    
    String chainId = params["chainId"];
    String pluginId = params["pluginId"];
    int position = params.contains("position") ? params["position"] : -1;
    
    String instanceId = vst3Host->loadPlugin(chainId, pluginId, position);
    
    json result;
    result["instanceId"] = instanceId.toStdString();
    return result;
}

json JsonRpcServer::handleGetAudioDevices(const json& params)
{
    json result = json::array();
    
    auto deviceNames = audioEngine->getAvailableDeviceNames();
    for (const auto& name : deviceNames) {
        result.push_back(name.toStdString());
    }
    
    return result;
}

void JsonRpcServer::registerAllMethods()
{
    registerMethod("listPlugins", [this](const json& params) { return handleListPlugins(params); });
    registerMethod("loadPlugin", [this](const json& params) { return handleLoadPlugin(params); });
    registerMethod("getMeters", [this](const json& params) { return handleGetMeters(params); });
    registerMethod("getAudioDevices", [this](const json& params) { return handleGetAudioDevices(params); });
    
    // TODO: Register remaining methods
    registerMethod("removePlugin", [this](const json& params) { return json::object(); });
    registerMethod("setParameter", [this](const json& params) { return json::object(); });
    registerMethod("getParameter", [this](const json& params) { return json::object(); });
    registerMethod("showEditor", [this](const json& params) { return json::object(); });
    registerMethod("hideEditor", [this](const json& params) { return json::object(); });
    registerMethod("saveChain", [this](const json& params) { return json::object(); });
    registerMethod("loadChain", [this](const json& params) { return json::object(); });
    registerMethod("addConnection", [this](const json& params) { return json::object(); });
    registerMethod("setAudioDevice", [this](const json& params) { return json::object(); });
    registerMethod("setBufferSize", [this](const json& params) { return json::object(); });
    registerMethod("setSampleRate", [this](const json& params) { return json::object(); });
}

json JsonRpcServer::createSuccessResponse(int id, const json& result)
{
    json response;
    response["jsonrpc"] = "2.0";
    response["id"] = id;
    response["result"] = result;
    return response;
}

json JsonRpcServer::createErrorResponse(int id, int code, const std::string& message)
{
    json response;
    response["jsonrpc"] = "2.0";
    response["id"] = id;
    response["error"]["code"] = code;
    response["error"]["message"] = message;
    return response;
}
