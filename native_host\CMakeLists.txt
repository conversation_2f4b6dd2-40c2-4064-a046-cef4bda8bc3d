cmake_minimum_required(VERSION 3.22)

project(DJMixerVSTHost VERSION 1.0.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include CPM for dependency management
include(cmake/CPM.cmake)

# Add JUCE
CPMAddPackage(
    NAME JUCE
    GITHUB_REPOSITORY juce-framework/JUCE
    GIT_TAG 7.0.12
    SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/JUCE
)

# Add nlohmann/json for IPC
CPMAddPackage(
    NAME nlohmann_json
    GITHUB_REPOSITORY nlohmann/json
    VERSION 3.11.3
)

# Add Catch2 for testing
CPMAddPackage(
    NAME Catch2
    GITHUB_REPOSITORY catchorg/Catch2
    VERSION 3.4.0
)

# Copy Ardour VST3 and audio engine code
set(ARDOUR_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../ardour")

# VST3 Host sources from Ardour
set(VST3_SOURCES
    src/vst3/vst3_host.cpp
    src/vst3/vst3_module.cpp
    src/vst3/vst3_plugin.cpp
    src/audio/audio_engine.cpp
    src/audio/audio_buffer.cpp
    src/audio/peak_meter.cpp
    src/ipc/json_rpc_server.cpp
    src/main.cpp
)

# Include directories
set(INCLUDE_DIRS
    src
    src/vst3
    src/audio
    src/ipc
    ${ARDOUR_SOURCE_DIR}/libs/ardour
    ${ARDOUR_SOURCE_DIR}/libs/ardour/ardour
    ${ARDOUR_SOURCE_DIR}/libs/pbd
    ${ARDOUR_SOURCE_DIR}/libs/temporal
    ${ARDOUR_SOURCE_DIR}/libs/vst3
)

# Create the executable
juce_add_console_app(DJMixerVSTHost
    PRODUCT_NAME "DJ Mixer VST Host"
    COMPANY_NAME "SpaceJ"
    VERSION ${PROJECT_VERSION}
)

target_sources(DJMixerVSTHost PRIVATE ${VST3_SOURCES})

target_include_directories(DJMixerVSTHost PRIVATE ${INCLUDE_DIRS})

# JUCE modules
target_link_libraries(DJMixerVSTHost PRIVATE
    juce::juce_core
    juce::juce_audio_basics
    juce::juce_audio_devices
    juce::juce_audio_formats
    juce::juce_audio_processors
    juce::juce_audio_utils
    juce::juce_events
    juce::juce_gui_basics
    nlohmann_json::nlohmann_json
)

# Platform-specific settings
if(WIN32)
    target_compile_definitions(DJMixerVSTHost PRIVATE
        JUCE_WASAPI=1
        JUCE_ASIO=1
        JUCE_VST3_CAN_REPLACE_VST2=0
    )
    target_link_libraries(DJMixerVSTHost PRIVATE
        winmm
        ole32
        oleaut32
        uuid
        advapi32
        shell32
        user32
        kernel32
        gdi32
        comdlg32
        comctl32
    )
elseif(APPLE)
    target_compile_definitions(DJMixerVSTHost PRIVATE
        JUCE_COREAUDIO=1
        JUCE_COREMIDI=1
    )
    target_link_libraries(DJMixerVSTHost PRIVATE
        "-framework CoreAudio"
        "-framework CoreMIDI"
        "-framework AudioUnit"
        "-framework AudioToolbox"
        "-framework Accelerate"
    )
elseif(UNIX)
    target_compile_definitions(DJMixerVSTHost PRIVATE
        JUCE_ALSA=1
        JUCE_JACK=1
    )
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(ALSA REQUIRED alsa)
    target_link_libraries(DJMixerVSTHost PRIVATE ${ALSA_LIBRARIES})
endif()

# Compiler flags
target_compile_options(DJMixerVSTHost PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /permissive->
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -Wpedantic>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -Wpedantic>
)

# Tests
if(Catch2_FOUND)
    juce_add_console_app(DJMixerVSTHost_Tests
        PRODUCT_NAME "DJ Mixer VST Host Tests"
    )
    
    target_sources(DJMixerVSTHost_Tests PRIVATE
        tests/test_main.cpp
        tests/test_vst3_host.cpp
        tests/test_audio_engine.cpp
        tests/test_ipc.cpp
    )
    
    target_link_libraries(DJMixerVSTHost_Tests PRIVATE
        Catch2::Catch2WithMain
        juce::juce_core
        juce::juce_audio_basics
        nlohmann_json::nlohmann_json
    )
    
    target_include_directories(DJMixerVSTHost_Tests PRIVATE ${INCLUDE_DIRS})
endif()
