#include "audio_engine.h"
#include <iostream>

AudioEngine::AudioEngine()
    : deviceManager(std::make_unique<AudioDeviceManager>())
{
    // Initialize audio buffers
    micBuffer.setSize(2, 1024); // Stereo, 1024 samples max
    masterBuffer.setSize(2, 1024);
}

AudioEngine::~AudioEngine()
{
    shutdown();
}

bool AudioEngine::initialize()
{
    std::lock_guard<std::mutex> lock(deviceMutex);
    
    try {
        // Initialize audio device types
        deviceManager->initialiseWithDefaultDevices(2, 2); // 2 inputs, 2 outputs
        
        // Set up audio callback
        deviceManager->addAudioCallback(this);
        
        // Get current device settings
        if (auto* device = deviceManager->getCurrentAudioDevice()) {
            currentBufferSize = device->getCurrentBufferSizeSamples();
            currentSampleRate = device->getCurrentSampleRate();
            
            std::cout << "Audio device: " << device->getName() << std::endl;
            std::cout << "Sample rate: " << currentSampleRate << " Hz" << std::endl;
            std::cout << "Buffer size: " << currentBufferSize << " samples" << std::endl;
            std::cout << "Latency: " << getLatencyMs() << " ms" << std::endl;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize audio engine: " << e.what() << std::endl;
        return false;
    }
}

void AudioEngine::shutdown()
{
    std::lock_guard<std::mutex> lock(deviceMutex);
    
    if (deviceManager) {
        deviceManager->removeAudioCallback(this);
        deviceManager->closeAudioDevice();
    }
    
    isProcessing.store(false);
}

StringArray AudioEngine::getAvailableDeviceNames() const
{
    std::lock_guard<std::mutex> lock(deviceMutex);
    
    StringArray deviceNames;
    
    for (auto& type : deviceManager->getAvailableDeviceTypes()) {
        type->scanForDevices();
        deviceNames.addArray(type->getDeviceNames());
    }
    
    return deviceNames;
}

bool AudioEngine::setAudioDevice(const String& deviceName)
{
    std::lock_guard<std::mutex> lock(deviceMutex);
    
    AudioDeviceManager::AudioDeviceSetup setup;
    deviceManager->getAudioDeviceSetup(setup);
    setup.outputDeviceName = deviceName;
    setup.inputDeviceName = deviceName;
    
    String error = deviceManager->setAudioDeviceSetup(setup, true);
    return error.isEmpty();
}

bool AudioEngine::setBufferSize(int bufferSize)
{
    std::lock_guard<std::mutex> lock(deviceMutex);
    
    if (auto* device = deviceManager->getCurrentAudioDevice()) {
        String error = device->open(device->getActiveInputChannels(),
                                   device->getActiveOutputChannels(),
                                   device->getCurrentSampleRate(),
                                   bufferSize);
        
        if (error.isEmpty()) {
            currentBufferSize = bufferSize;
            return true;
        }
    }
    
    return false;
}

bool AudioEngine::setSampleRate(double sampleRate)
{
    std::lock_guard<std::mutex> lock(deviceMutex);
    
    if (auto* device = deviceManager->getCurrentAudioDevice()) {
        String error = device->open(device->getActiveInputChannels(),
                                   device->getActiveOutputChannels(),
                                   sampleRate,
                                   device->getCurrentBufferSizeSamples());
        
        if (error.isEmpty()) {
            currentSampleRate = sampleRate;
            return true;
        }
    }
    
    return false;
}

void AudioEngine::audioDeviceIOCallbackWithContext(const float* const* inputChannelData,
                                                  int numInputChannels,
                                                  float* const* outputChannelData,
                                                  int numOutputChannels,
                                                  int numSamples,
                                                  const AudioIODeviceCallbackContext& context)
{
    // Clear output buffers
    for (int channel = 0; channel < numOutputChannels; ++channel) {
        FloatVectorOperations::clear(outputChannelData[channel], numSamples);
    }
    
    // Update buffer sizes if needed
    if (micBuffer.getNumSamples() < numSamples) {
        micBuffer.setSize(micBuffer.getNumChannels(), numSamples, true, true, true);
        masterBuffer.setSize(masterBuffer.getNumChannels(), numSamples, true, true, true);
    }
    
    // Process microphone input
    processMicInput(inputChannelData, outputChannelData, 
                   numInputChannels, numOutputChannels, numSamples);
    
    // Update meters
    updateMeters(inputChannelData, outputChannelData,
                numInputChannels, numOutputChannels, numSamples);
    
    isProcessing.store(true);
}

void AudioEngine::audioDeviceAboutToStart(AudioIODevice* device)
{
    currentBufferSize = device->getCurrentBufferSizeSamples();
    currentSampleRate = device->getCurrentSampleRate();
    
    std::cout << "Audio device starting: " << device->getName() << std::endl;
    std::cout << "Buffer size: " << currentBufferSize << " samples" << std::endl;
    std::cout << "Sample rate: " << currentSampleRate << " Hz" << std::endl;
}

void AudioEngine::audioDeviceStopped()
{
    isProcessing.store(false);
    std::cout << "Audio device stopped" << std::endl;
}

void AudioEngine::connectMicInput(int inputChannel)
{
    micInputChannel = inputChannel;
    std::cout << "Microphone connected to input channel " << inputChannel << std::endl;
}

void AudioEngine::disconnectMicInput()
{
    micInputChannel = -1;
    std::cout << "Microphone disconnected" << std::endl;
}

int AudioEngine::getCurrentBufferSize() const
{
    return currentBufferSize;
}

double AudioEngine::getCurrentSampleRate() const
{
    return currentSampleRate;
}

double AudioEngine::getLatencyMs() const
{
    return (currentBufferSize / currentSampleRate) * 1000.0;
}

void AudioEngine::updateMeters(const float* const* inputData, 
                              float* const* outputData,
                              int numInputChannels,
                              int numOutputChannels, 
                              int numSamples)
{
    // Update mic level meter
    if (micInputChannel >= 0 && micInputChannel < numInputChannels && inputData[micInputChannel]) {
        float level = FloatVectorOperations::findMaximum(inputData[micInputChannel], numSamples);
        micLevel.store(level);
    }
    
    // Update master level meter
    if (numOutputChannels > 0 && outputData[0]) {
        float level = FloatVectorOperations::findMaximum(outputData[0], numSamples);
        masterLevel.store(level);
    }
}

void AudioEngine::processMicInput(const float* const* inputData,
                                 float* const* outputData,
                                 int numInputChannels,
                                 int numOutputChannels,
                                 int numSamples)
{
    // Simple mic monitoring - copy input to output with gain
    if (micInputChannel >= 0 && micInputChannel < numInputChannels && 
        !micMuted.load() && inputData[micInputChannel]) {
        
        float gain = micGain.load();
        
        for (int channel = 0; channel < std::min(numOutputChannels, 2); ++channel) {
            if (outputData[channel]) {
                FloatVectorOperations::addWithMultiply(outputData[channel], 
                                                      inputData[micInputChannel], 
                                                      gain, numSamples);
            }
        }
    }
}
