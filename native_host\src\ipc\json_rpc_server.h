#pragma once

#include <nlohmann/json.hpp>
#include <memory>
#include <thread>
#include <atomic>
#include <functional>
#include <map>
#include <string>

// Forward declarations
class AudioEngine;
class VST3Host;

using json = nlohmann::json;

/**
 * JSON-RPC server for IPC communication with Electron frontend
 * Handles stdio-based communication for cross-platform compatibility
 */
class JsonRpcServer
{
public:
    using MethodHandler = std::function<json(const json& params)>;

    JsonRpcServer(AudioEngine* audioEngine, VST3Host* vst3Host);
    ~JsonRpcServer();

    // Server lifecycle
    bool start();
    void stop();
    bool isRunning() const { return running.load(); }

    // Method registration
    void registerMethod(const std::string& methodName, MethodHandler handler);

private:
    AudioEngine* audioEngine;
    VST3Host* vst3Host;
    
    std::atomic<bool> running{false};
    std::unique_ptr<std::thread> serverThread;
    std::unique_ptr<std::thread> metricsThread;
    
    std::map<std::string, MethodHandler> methods;
    
    // Core server methods
    void serverLoop();
    void metricsLoop();
    void processRequest(const std::string& line);
    void sendResponse(const json& response);
    void sendError(int id, int code, const std::string& message);
    
    // RPC method implementations
    json handleListPlugins(const json& params);
    json handleLoadPlugin(const json& params);
    json handleRemovePlugin(const json& params);
    json handleSetParameter(const json& params);
    json handleGetParameter(const json& params);
    json handleShowEditor(const json& params);
    json handleHideEditor(const json& params);
    json handleSaveChain(const json& params);
    json handleLoadChain(const json& params);
    json handleAddConnection(const json& params);
    json handleGetMeters(const json& params);
    json handleSetAudioDevice(const json& params);
    json handleGetAudioDevices(const json& params);
    json handleSetBufferSize(const json& params);
    json handleSetSampleRate(const json& params);
    
    // Utility methods
    void registerAllMethods();
    json createSuccessResponse(int id, const json& result);
    json createErrorResponse(int id, int code, const std::string& message);
    
    // Error codes
    static constexpr int ERROR_PARSE_ERROR = -32700;
    static constexpr int ERROR_INVALID_REQUEST = -32600;
    static constexpr int ERROR_METHOD_NOT_FOUND = -32601;
    static constexpr int ERROR_INVALID_PARAMS = -32602;
    static constexpr int ERROR_INTERNAL_ERROR = -32603;
    static constexpr int ERROR_PLUGIN_NOT_FOUND = -32001;
    static constexpr int ERROR_AUDIO_ENGINE_ERROR = -32002;
    static constexpr int ERROR_VST3_ERROR = -32003;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(JsonRpcServer)
};
