#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

#include <juce_core/juce_core.h>
#include <juce_audio_basics/juce_audio_basics.h>
#include <juce_audio_devices/juce_audio_devices.h>
#include <juce_audio_processors/juce_audio_processors.h>

#include "audio/audio_engine.h"
#include "ipc/json_rpc_server.h"
#include "vst3/vst3_host.h"

using namespace juce;

class DJMixerVSTHostApplication : public JUCEApplication
{
public:
    DJMixerVSTHostApplication() = default;

    const String getApplicationName() override { return "DJ Mixer VST Host"; }
    const String getApplicationVersion() override { return "1.0.0"; }
    bool moreThanOneInstanceAllowed() override { return false; }

    void initialise(const String& commandLine) override
    {
        std::cout << "HELLO AUDIO - DJ Mixer VST Host Starting..." << std::endl;
        
        // Parse command line arguments
        StringArray args = getCommandLineParameterArray();
        bool bridgeMode = args.contains("--bridge");
        
        if (bridgeMode) {
            std::cout << "Bridge mode enabled - plugins will run in separate processes" << std::endl;
        }

        try {
            // Initialize audio engine
            audioEngine = std::make_unique<AudioEngine>();
            if (!audioEngine->initialize()) {
                std::cerr << "Failed to initialize audio engine" << std::endl;
                quit();
                return;
            }
            
            // Initialize VST3 host
            vst3Host = std::make_unique<VST3Host>(bridgeMode);
            if (!vst3Host->initialize()) {
                std::cerr << "Failed to initialize VST3 host" << std::endl;
                quit();
                return;
            }
            
            // Initialize IPC server
            ipcServer = std::make_unique<JsonRpcServer>(audioEngine.get(), vst3Host.get());
            if (!ipcServer->start()) {
                std::cerr << "Failed to start IPC server" << std::endl;
                quit();
                return;
            }
            
            std::cout << "DJ Mixer VST Host initialized successfully!" << std::endl;
            std::cout << "Audio devices available: " << audioEngine->getAvailableDeviceNames().size() << std::endl;
            std::cout << "VST3 plugins found: " << vst3Host->getAvailablePlugins().size() << std::endl;
            std::cout << "IPC server listening on stdio..." << std::endl;
            
            // Keep the application running
            isRunning = true;
            
        } catch (const std::exception& e) {
            std::cerr << "Error during initialization: " << e.what() << std::endl;
            quit();
        }
    }

    void shutdown() override
    {
        std::cout << "Shutting down DJ Mixer VST Host..." << std::endl;
        
        isRunning = false;
        
        if (ipcServer) {
            ipcServer->stop();
            ipcServer.reset();
        }
        
        if (vst3Host) {
            vst3Host->shutdown();
            vst3Host.reset();
        }
        
        if (audioEngine) {
            audioEngine->shutdown();
            audioEngine.reset();
        }
        
        std::cout << "Shutdown complete." << std::endl;
    }

    void systemRequestedQuit() override
    {
        quit();
    }

    void anotherInstanceStarted(const String& commandLine) override
    {
        // Handle multiple instance attempts
        std::cout << "Another instance attempted to start with: " << commandLine << std::endl;
    }

private:
    std::unique_ptr<AudioEngine> audioEngine;
    std::unique_ptr<VST3Host> vst3Host;
    std::unique_ptr<JsonRpcServer> ipcServer;
    bool isRunning = false;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(DJMixerVSTHostApplication)
};

// This macro generates the main() routine that launches the app.
START_JUCE_APPLICATION(DJMixerVSTHostApplication)
