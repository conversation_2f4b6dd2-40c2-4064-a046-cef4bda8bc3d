#include "vst3_host.h"
#include <iostream>
#include <filesystem>

VST3Host::VST3Host(bool bridgeMode)
    : bridgeMode(bridgeMode)
{
}

VST3Host::~VST3Host()
{
    shutdown();
}

bool VST3Host::initialize()
{
    std::cout << "Initializing VST3 Host..." << std::endl;
    
    try {
        // Scan for VST3 plugins
        if (!scanPlugins()) {
            std::cerr << "Warning: No VST3 plugins found" << std::endl;
        }
        
        // Initialize default chains
        chains["DeckA"] = std::vector<String>();
        chains["DeckB"] = std::vector<String>();
        chains["Mic"] = std::vector<String>();
        chains["Master"] = std::vector<String>();
        
        std::cout << "VST3 Host initialized with " << availablePlugins.size() << " plugins" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize VST3 Host: " << e.what() << std::endl;
        return false;
    }
}

void VST3Host::shutdown()
{
    std::lock_guard<std::mutex> pluginLock(pluginMutex);
    std::lock_guard<std::mutex> chainLock(chainMutex);
    
    // Clean up all plugin instances
    pluginInstances.clear();
    chains.clear();
    availablePlugins.clear();
    
    std::cout << "VST3 Host shutdown complete" << std::endl;
}

std::vector<VST3Host::PluginInfo> VST3Host::getAvailablePlugins() const
{
    std::lock_guard<std::mutex> lock(pluginMutex);
    return availablePlugins;
}

bool VST3Host::scanPlugins(const String& directory)
{
    std::cout << "Scanning for VST3 plugins..." << std::endl;
    
    availablePlugins.clear();
    
    // Default VST3 directories
    std::vector<String> scanDirs;
    
    if (!directory.isEmpty()) {
        scanDirs.push_back(directory);
    } else {
        // Platform-specific default directories
#ifdef _WIN32
        scanDirs.push_back("C:\\Program Files\\Common Files\\VST3");
        scanDirs.push_back("C:\\Program Files (x86)\\Common Files\\VST3");
#elif __APPLE__
        scanDirs.push_back("/Library/Audio/Plug-Ins/VST3");
        scanDirs.push_back("~/Library/Audio/Plug-Ins/VST3");
#else
        scanDirs.push_back("/usr/lib/vst3");
        scanDirs.push_back("~/.vst3");
#endif
        // Also scan embedded resources/vst directory
        scanDirs.push_back("resources/vst");
    }
    
    for (const auto& dir : scanDirs) {
        scanDirectory(dir);
    }
    
    std::cout << "Found " << availablePlugins.size() << " VST3 plugins" << std::endl;
    return !availablePlugins.empty();
}

String VST3Host::loadPlugin(const String& chainId, const String& pluginId, int position)
{
    std::lock_guard<std::mutex> pluginLock(pluginMutex);
    std::lock_guard<std::mutex> chainLock(chainMutex);
    
    // Find plugin info
    auto pluginIt = std::find_if(availablePlugins.begin(), availablePlugins.end(),
        [&pluginId](const PluginInfo& info) { return info.id == pluginId; });
    
    if (pluginIt == availablePlugins.end()) {
        std::cerr << "Plugin not found: " << pluginId << std::endl;
        return "";
    }
    
    // Create instance
    auto instance = std::make_unique<PluginInstance>();
    instance->id = generateInstanceId();
    instance->pluginId = pluginId;
    instance->chainId = chainId;
    instance->position = position;
    
    // TODO: Load actual VST3 plugin using Ardour code
    // For now, just create a placeholder
    std::cout << "Loading plugin: " << pluginIt->name << " into chain: " << chainId << std::endl;
    
    String instanceId = instance->id;
    pluginInstances[instanceId] = std::move(instance);
    
    // Add to chain
    if (chains.find(chainId) != chains.end()) {
        if (position >= 0 && position < chains[chainId].size()) {
            chains[chainId].insert(chains[chainId].begin() + position, instanceId);
        } else {
            chains[chainId].push_back(instanceId);
        }
    }
    
    return instanceId;
}

bool VST3Host::removePlugin(const String& chainId, const String& instanceId)
{
    std::lock_guard<std::mutex> pluginLock(pluginMutex);
    std::lock_guard<std::mutex> chainLock(chainMutex);
    
    // Remove from chain
    if (chains.find(chainId) != chains.end()) {
        auto& chain = chains[chainId];
        chain.erase(std::remove(chain.begin(), chain.end(), instanceId), chain.end());
    }
    
    // Remove instance
    auto it = pluginInstances.find(instanceId);
    if (it != pluginInstances.end()) {
        std::cout << "Removing plugin instance: " << instanceId << std::endl;
        pluginInstances.erase(it);
        return true;
    }
    
    return false;
}

bool VST3Host::setParameter(const String& instanceId, int parameterId, float value)
{
    std::lock_guard<std::mutex> lock(pluginMutex);
    
    auto it = pluginInstances.find(instanceId);
    if (it != pluginInstances.end()) {
        it->second->parameters[parameterId] = value;
        // TODO: Set actual VST3 parameter
        std::cout << "Set parameter " << parameterId << " = " << value 
                  << " for instance " << instanceId << std::endl;
        return true;
    }
    
    return false;
}

float VST3Host::getParameter(const String& instanceId, int parameterId) const
{
    std::lock_guard<std::mutex> lock(pluginMutex);
    
    auto it = pluginInstances.find(instanceId);
    if (it != pluginInstances.end()) {
        auto paramIt = it->second->parameters.find(parameterId);
        if (paramIt != it->second->parameters.end()) {
            return paramIt->second;
        }
    }
    
    return 0.0f;
}

std::vector<String> VST3Host::getChainIds() const
{
    std::lock_guard<std::mutex> lock(chainMutex);
    
    std::vector<String> chainIds;
    for (const auto& pair : chains) {
        chainIds.push_back(pair.first);
    }
    
    return chainIds;
}

void VST3Host::processAudio(const String& chainId, AudioBuffer<float>& buffer, int numSamples)
{
    std::lock_guard<std::mutex> chainLock(chainMutex);
    
    auto chainIt = chains.find(chainId);
    if (chainIt != chains.end()) {
        processPluginChain(chainIt->second, buffer, numSamples);
    }
}

std::map<String, float> VST3Host::getCurrentMeters() const
{
    std::map<String, float> meters;
    
    // TODO: Implement actual metering from plugins
    meters["DeckA"] = 0.0f;
    meters["DeckB"] = 0.0f;
    meters["Mic"] = 0.0f;
    meters["Master"] = 0.0f;
    
    return meters;
}

void VST3Host::scanDirectory(const String& directory)
{
    File dir(directory);
    if (!dir.exists() || !dir.isDirectory()) {
        return;
    }
    
    std::cout << "Scanning directory: " << directory << std::endl;
    
    for (const auto& entry : RangedDirectoryIterator(dir, false, "*.vst3", File::findFilesAndDirectories)) {
        if (entry.getFile().isDirectory()) {
            scanVST3Bundle(entry.getFile());
        }
    }
}

void VST3Host::scanVST3Bundle(const File& bundleFile)
{
    // TODO: Implement actual VST3 bundle scanning using Ardour code
    // For now, create a placeholder plugin info
    PluginInfo info;
    info.id = bundleFile.getFileNameWithoutExtension();
    info.name = bundleFile.getFileNameWithoutExtension();
    info.vendor = "Unknown";
    info.category = "Effect";
    info.path = bundleFile.getFullPathName();
    info.numInputs = 2;
    info.numOutputs = 2;
    info.isInstrument = false;
    
    availablePlugins.push_back(info);
    std::cout << "Found plugin: " << info.name << std::endl;
}

String VST3Host::generateInstanceId() const
{
    return "instance_" + String(Time::getCurrentTime().toMilliseconds());
}

VST3Host::ChainType VST3Host::getChainType(const String& chainId) const
{
    if (chainId == "DeckA") return ChainType::DeckA;
    if (chainId == "DeckB") return ChainType::DeckB;
    if (chainId == "Mic") return ChainType::Mic;
    if (chainId == "Master") return ChainType::Master;
    return ChainType::Mic; // Default
}

void VST3Host::processPluginChain(const std::vector<String>& instanceIds,
                                 AudioBuffer<float>& buffer,
                                 int numSamples)
{
    // TODO: Process audio through plugin chain
    // For now, just pass through
    for (const auto& instanceId : instanceIds) {
        auto it = pluginInstances.find(instanceId);
        if (it != pluginInstances.end() && !it->second->bypassed) {
            // Process audio through plugin
            // This would use Ardour's VST3PI::process method
        }
    }
}
